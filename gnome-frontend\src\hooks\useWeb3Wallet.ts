import { useState, useEffect } from 'preact/hooks';

interface WalletState {
  isConnected: boolean;
  address: string | null;
  chainId: number | null;
  isConnecting: boolean;
  error: string | null;
}

export function useWeb3Wallet() {
  const [state, setState] = useState<WalletState>({
    isConnected: false,
    address: null,
    chainId: null,
    isConnecting: false,
    error: null
  });

  // Check if wallet is already connected on mount
  useEffect(() => {
    checkConnection();
    
    // Listen for account changes
    if (window.ethereum) {
      window.ethereum.on('accountsChanged', handleAccountsChanged);
      window.ethereum.on('chainChanged', handleChainChanged);
      window.ethereum.on('disconnect', handleDisconnect);
    }

    return () => {
      if (window.ethereum) {
        window.ethereum.removeListener('accountsChanged', handleAccountsChanged);
        window.ethereum.removeListener('chainChanged', handleChainChanged);
        window.ethereum.removeListener('disconnect', handleDisconnect);
      }
    };
  }, []);

  const checkConnection = async () => {
    if (!window.ethereum) {
      setState(prev => ({ ...prev, error: 'No wallet found' }));
      return;
    }

    try {
      const accounts = await window.ethereum.request({ method: 'eth_accounts' });
      const chainId = await window.ethereum.request({ method: 'eth_chainId' });
      
      if (accounts && accounts.length > 0) {
        setState({
          isConnected: true,
          address: accounts[0],
          chainId: parseInt(chainId, 16),
          isConnecting: false,
          error: null
        });
      } else {
        setState(prev => ({
          ...prev,
          isConnected: false,
          address: null,
          isConnecting: false
        }));
      }
    } catch (error: any) {
      console.error('Error checking connection:', error);
      setState(prev => ({ ...prev, error: error.message, isConnecting: false }));
    }
  };

  const connect = async () => {
    if (!window.ethereum) {
      setState(prev => ({ ...prev, error: 'No wallet found. Please install MetaMask or another Web3 wallet.' }));
      return false;
    }

    setState(prev => ({ ...prev, isConnecting: true, error: null }));

    try {
      console.log('🚀 Requesting wallet connection...');
      
      // Request account access
      const accounts = await window.ethereum.request({
        method: 'eth_requestAccounts'
      });

      console.log('✅ Accounts received:', accounts);

      if (!accounts || accounts.length === 0) {
        throw new Error('No accounts available. Please unlock your wallet and try again.');
      }

      // Get chain ID
      const chainId = await window.ethereum.request({ method: 'eth_chainId' });
      
      setState({
        isConnected: true,
        address: accounts[0],
        chainId: parseInt(chainId, 16),
        isConnecting: false,
        error: null
      });

      console.log('✅ Wallet connected successfully!');
      return true;

    } catch (error: any) {
      console.error('❌ Connection failed:', error);
      
      let errorMessage = 'Connection failed';
      
      if (error.code === 4001) {
        errorMessage = 'Connection request was rejected';
      } else if (error.message.includes('wallet must has at least one account')) {
        errorMessage = 'Please unlock your wallet and make sure you have at least one account';
      } else if (error.message.includes('User rejected')) {
        errorMessage = 'Connection request was rejected';
      } else {
        errorMessage = error.message || 'Unknown error occurred';
      }

      setState(prev => ({
        ...prev,
        isConnecting: false,
        error: errorMessage
      }));

      return false;
    }
  };

  const disconnect = () => {
    setState({
      isConnected: false,
      address: null,
      chainId: null,
      isConnecting: false,
      error: null
    });
  };

  const signMessage = async (message: string): Promise<string> => {
    if (!window.ethereum || !state.address) {
      throw new Error('Wallet not connected');
    }

    try {
      const signature = await window.ethereum.request({
        method: 'personal_sign',
        params: [message, state.address]
      });

      return signature;
    } catch (error: any) {
      console.error('Signing failed:', error);
      throw new Error(error.message || 'Failed to sign message');
    }
  };

  // Event handlers
  const handleAccountsChanged = (accounts: string[]) => {
    console.log('Accounts changed:', accounts);
    if (accounts.length === 0) {
      disconnect();
    } else {
      setState(prev => ({
        ...prev,
        address: accounts[0],
        isConnected: true
      }));
    }
  };

  const handleChainChanged = (chainId: string) => {
    console.log('Chain changed:', chainId);
    setState(prev => ({
      ...prev,
      chainId: parseInt(chainId, 16)
    }));
  };

  const handleDisconnect = () => {
    console.log('Wallet disconnected');
    disconnect();
  };

  return {
    ...state,
    connect,
    disconnect,
    signMessage,
    checkConnection
  };
}
