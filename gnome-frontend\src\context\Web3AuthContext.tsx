import { createContext, useContext, useState, useEffect } from 'preact/compat';
import type { ComponentChildren } from 'preact';
import { useWeb3Wallet } from '../hooks/useWeb3Wallet';
import { authApi } from '../services/api';

interface Web3AuthContextType {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: any | null;
  login: () => Promise<void>;
  logout: () => void;
  error: string | null;
  // Wallet state
  isConnected: boolean;
  address: string | null;
  chainId: number | null;
  isConnecting: boolean;
  walletError: string | null;
  connect: () => Promise<boolean>;
  disconnect: () => void;
}

const Web3AuthContext = createContext<Web3AuthContextType | undefined>(undefined);

export function Web3AuthProvider({ children }: { children: ComponentChildren }) {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [user, setUser] = useState<any | null>(null);
  const [error, setError] = useState<string | null>(null);
  
  const wallet = useWeb3Wallet();

  // Check if user is already authenticated on mount
  useEffect(() => {
    const checkAuth = async () => {
      const token = localStorage.getItem('accessToken');
      if (token) {
        try {
          const profile = await authApi.getProfile();
          setUser(profile);
          setIsAuthenticated(true);
        } catch (error) {
          // Token is invalid or expired
          localStorage.removeItem('accessToken');
          setIsAuthenticated(false);
          setUser(null);
        }
      }
      setIsLoading(false);
    };

    checkAuth();
  }, []);

  // Auto-login when wallet connects
  useEffect(() => {
    if (wallet.isConnected && wallet.address && !isAuthenticated && !isLoading) {
      console.log('Wallet connected, attempting auto-login...');
      login();
    }
  }, [wallet.isConnected, wallet.address, isAuthenticated, isLoading]);

  // Login function to authenticate user with wallet
  const login = async () => {
    if (!wallet.address || !wallet.isConnected) {
      setError('Wallet not connected');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      console.log('Starting authentication process with address:', wallet.address);
      
      // Create a message for the user to sign
      const timestamp = Date.now();
      const message = `Sign this message to authenticate with The Cave: ${timestamp}`;
      console.log('Message to sign:', message);
      
      // Request signature from the wallet
      console.log('Requesting signature...');
      const signature = await wallet.signMessage(message);
      console.log('Signature received:', signature);
      
      // Send the signature to the backend for verification
      console.log('Sending signature to backend...');
      const authResponse = await authApi.signin(message, signature, wallet.address);
      console.log('Auth response:', authResponse);
      
      // Save the access token
      localStorage.setItem('accessToken', authResponse.accessToken);
      
      // Get user profile
      console.log('Getting user profile...');
      const profile = await authApi.getProfile();
      console.log('User profile:', profile);
      setUser(profile);
      setIsAuthenticated(true);
      
      // Redirect to dashboard
      window.location.href = '/dashboard';
      
    } catch (error: any) {
      console.error('Authentication error:', error);
      
      let errorMessage = 'Authentication failed';
      
      if (error.message.includes('User rejected') || error.message.includes('denied')) {
        errorMessage = 'Signature request was rejected';
      } else if (error.response) {
        errorMessage = error.response.data.message || 'Authentication failed';
      } else {
        errorMessage = error.message || 'Authentication failed';
      }
      
      setError(errorMessage);
      setIsAuthenticated(false);
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  // Logout function
  const logout = () => {
    localStorage.removeItem('accessToken');
    setIsAuthenticated(false);
    setUser(null);
    wallet.disconnect();
  };

  return (
    <Web3AuthContext.Provider
      value={{
        isAuthenticated,
        isLoading,
        user,
        login,
        logout,
        error,
        // Wallet state
        isConnected: wallet.isConnected,
        address: wallet.address,
        chainId: wallet.chainId,
        isConnecting: wallet.isConnecting,
        walletError: wallet.error,
        connect: wallet.connect,
        disconnect: wallet.disconnect,
      }}
    >
      {children}
    </Web3AuthContext.Provider>
  );
}

// Custom hook to use the auth context
export function useWeb3Auth() {
  const context = useContext(Web3AuthContext);
  if (context === undefined) {
    throw new Error('useWeb3Auth must be used within a Web3AuthProvider');
  }
  return context;
}
