import { useConnect, useAccount, useDisconnect } from 'wagmi';
import { useEffect } from 'preact/hooks';
import { useAuth } from '../../context/AuthContext';
import { isMobileMetaMask } from '../../utils/wallet-utils';

export function ConnectWallet() {
  const { connect, connectors, isPending } = useConnect();
  
  const { address, isConnected } = useAccount();
  const { disconnect } = useDisconnect();
  const { login, isAuthenticated, isLoading: authLoading } = useAuth();

  // Attempt to authenticate when wallet is connected
  useEffect(() => {
    const attemptLogin = async () => {
      if (isConnected && address && !isAuthenticated && !authLoading) {
        console.log('Wallet connected, attempting login with address:', address);
        await login();
      }
    };

    attemptLogin();
  }, [isConnected, address, isAuthenticated, authLoading, login]);

  // Handle wallet connection
  const handleConnect = async () => {
    // Use the utility function to detect mobile MetaMask
    const mobileMetaMaskDetected = isMobileMetaMask();
    
    try {
      if (isConnected) {
        console.log('Already connected, attempting to authenticate...');
        await login();
        return;
      }
      
      console.log('Connecting wallet...');
      
      // Special handling for mobile MetaMask browser
      if (mobileMetaMaskDetected) {
        console.log('Mobile MetaMask browser detected, using direct provider approach...');
        
        // For mobile MetaMask, we need to handle the connection differently
        if (window.ethereum) {
          try {
            // Request accounts directly from the provider
            console.log('Requesting accounts from injected provider...');
            const accounts = await window.ethereum.request({
              method: 'eth_requestAccounts'
            });
            
            console.log('Accounts received:', accounts);
            
            // After getting accounts, trigger login directly
            console.log('Triggering direct login after account connection...');
            await login();
            return;
          } catch (providerError: any) {
            console.error('Provider connection error:', providerError);
            
            // Handle specific error codes
            if (providerError.code === 4001) {
              console.error('User rejected the connection request');
              return;
            } else if (providerError.code === -32002) {
              console.error('Connection request is already pending');
              // Try to trigger login directly in case connection is already established
              try {
                await login();
                return;
              } catch (loginError) {
                console.error('Direct login failed:', loginError);
              }
            } else if (providerError.code === -32603) {
              console.error('Internal error occurred');
              // Try to trigger login directly in case connection is already established
              try {
                await login();
                return;
              } catch (loginError) {
                console.error('Direct login failed:', loginError);
              }
            }
            
            // For mobile browsers, if we get an error about no accounts, try to trigger login directly
            if (providerError.message && providerError.message.includes('wallet must has at least one account')) {
              console.log('No accounts error detected, attempting direct login...');
              try {
                await login();
                return;
              } catch (loginError) {
                console.error('Direct login failed:', loginError);
              }
            }
          }
        }
      }
      
      // Get the appropriate connector based on environment
      let connectorToUse;
      
      if (mobileMetaMaskDetected) {
        console.log('Mobile MetaMask browser detected, using injected connector...');
        // For mobile MetaMask, prioritize injected connector
        connectorToUse = connectors.find(c => c.id === 'injected') || connectors[0];
      } else {
        // Desktop or other environments
        console.log('Desktop browser detected, using MetaMask connector...');
        connectorToUse = connectors.find(c => c.name === 'MetaMask') || connectors[0];
      }
      
      if (!connectorToUse) {
        console.error('No connectors available');
        return;
      }
      
      console.log('Using connector:', connectorToUse.name, connectorToUse.id);
      await connect({ connector: connectorToUse });
      
      // For mobile browsers, we might need to wait a bit for the connection to establish
      if (mobileMetaMaskDetected) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    } catch (error: any) {
      console.error('Connection failed:', error);
      
      // Handle mobile-specific errors
      if (error instanceof Error) {
        if (error.message.includes('User rejected') || error.message.includes('denied')) {
          console.error('User rejected connection request');
        } else if (mobileMetaMaskDetected) {
          console.error('Mobile web3 browser connection issue:', error);
        }
      }
      
      // Handle specific error codes
      if (error.code === 4001) {
        console.error('User rejected the connection request');
      } else if (error.code === -32002) {
        console.error('Connection request is already pending');
        // Try to trigger login directly in case connection is already established
        try {
          await login();
        } catch (loginError) {
          console.error('Direct login failed:', loginError);
        }
      } else if (error.code === -32603) {
        console.error('Internal error occurred');
        // Try to trigger login directly in case connection is already established
        try {
          await login();
        } catch (loginError) {
          console.error('Direct login failed:', loginError);
        }
      }
      
      // For mobile browsers, if we get an error about no accounts, try to trigger login directly
      if (error.message && error.message.includes('wallet must has at least one account')) {
        console.log('No accounts error detected, attempting direct login...');
        try {
          await login();
        } catch (loginError) {
          console.error('Direct login failed:', loginError);
        }
      }
      
      // If we're in a mobile browser and get any connection error, try direct login after a delay
      if (mobileMetaMaskDetected) {
        console.log('Mobile browser detected, attempting delayed login...');
        setTimeout(async () => {
          try {
            await login();
          } catch (loginError) {
            console.error('Delayed login failed:', loginError);
          }
        }, 1500);
      }
    }
  };

  // Handle wallet disconnection
  const handleDisconnect = () => {
    disconnect();
  };

  return (
    <button 
      className="connect-wallet-btn rounded pixelated-text"
      onClick={isAuthenticated ? handleDisconnect : handleConnect}
      disabled={isPending || authLoading}
    >
      {isPending
        ? 'Connecting...'
        : authLoading
        ? 'Authenticating...'
        : isAuthenticated
        ? 'Disconnect'
        : isConnected
        ? 'Authenticate'
        : 'Connect Wallet'}
    </button>
  );
}
