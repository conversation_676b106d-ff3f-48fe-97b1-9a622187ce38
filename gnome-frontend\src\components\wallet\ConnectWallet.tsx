import { useAccount, useDisconnect } from 'wagmi';
import { useEffect, useState } from 'preact/hooks';
import { useAuth } from '../../context/AuthContext';

export function ConnectWallet() {
  const { address, isConnected } = useAccount();
  const { disconnect } = useDisconnect();
  const { login, isAuthenticated, isLoading: authLoading } = useAuth();
  const [isConnecting, setIsConnecting] = useState(false);

  // Auto-login when wallet connects
  useEffect(() => {
    if (isConnected && address && !isAuthenticated && !authLoading) {
      console.log('Wallet connected, attempting auto-login...');
      login();
    }
  }, [isConnected, address, isAuthenticated, authLoading, login]);

  // BULLETPROOF: Direct wallet connection without wagmi connectors
  const handleConnect = async () => {
    if (isConnecting) return;

    try {
      setIsConnecting(true);

      // If already connected, just authenticate
      if (isConnected) {
        await login();
        return;
      }

      console.log('🚀 Starting direct wallet connection...');

      // Check if any wallet is available
      if (!window.ethereum) {
        alert('No wallet found! Please install MetaMask or another Web3 wallet.');
        return;
      }

      console.log('✅ Wallet detected, requesting accounts...');

      // Direct connection to any injected wallet
      const accounts = await window.ethereum.request({
        method: 'eth_requestAccounts'
      });

      console.log('✅ Accounts received:', accounts);

      if (accounts && accounts.length > 0) {
        console.log('✅ Connection successful! Waiting for wagmi to sync...');

        // Wait for wagmi to detect the connection
        let attempts = 0;
        const maxAttempts = 10;

        while (attempts < maxAttempts && !isConnected) {
          await new Promise(resolve => setTimeout(resolve, 500));
          attempts++;
          console.log(`⏳ Waiting for connection sync... (${attempts}/${maxAttempts})`);
        }

        if (!isConnected) {
          console.log('🔄 Manual trigger login...');
          // If wagmi doesn't sync, trigger login manually
          await login();
        }
      }

    } catch (error: any) {
      console.error('💥 Connection failed:', error);

      if (error.code === 4001) {
        console.log('❌ User rejected connection');
      } else {
        alert(`Connection failed: ${error.message || 'Unknown error'}`);
      }
    } finally {
      setIsConnecting(false);
    }
  };

  // Handle wallet disconnection
  const handleDisconnect = () => {
    disconnect();
  };

  return (
    <button
      className="connect-wallet-btn rounded pixelated-text"
      onClick={isAuthenticated ? handleDisconnect : handleConnect}
      disabled={isConnecting || authLoading}
    >
      {isConnecting
        ? 'Connecting...'
        : authLoading
        ? 'Authenticating...'
        : isAuthenticated
        ? 'Disconnect'
        : isConnected
        ? 'Authenticate'
        : 'Connect Wallet'}
    </button>
  );
}
