import { useConnect, useAccount, useDisconnect } from 'wagmi';
import { useEffect } from 'preact/hooks';
import { useAuth } from '../../context/AuthContext';
import { isMobile } from '../../utils/wallet-utils';

export function ConnectWallet() {
  const { connect, connectors, isPending } = useConnect();
  const { address, isConnected } = useAccount();
  const { disconnect } = useDisconnect();
  const { login, isAuthenticated, isLoading: authLoading } = useAuth();

  // Auto-login when wallet connects
  useEffect(() => {
    if (isConnected && address && !isAuthenticated && !authLoading) {
      console.log('Wallet connected, attempting auto-login...');
      login();
    }
  }, [isConnected, address, isAuthenticated, authLoading, login]);

  // DEBUG: Simple wallet connection with extensive logging
  const handleConnect = async () => {
    console.log('🔥 CONNECT BUTTON CLICKED!');
    console.log('🔍 Current state:', {
      isConnected,
      isAuthenticated,
      isPending,
      authLoading,
      connectorsCount: connectors.length,
      hasEthereum: !!window.ethereum,
      isMobileDevice: isMobile()
    });

    try {
      // If already connected, just try to authenticate
      if (isConnected) {
        console.log('✅ Already connected, attempting authentication...');
        await login();
        return;
      }

      console.log('🚀 Starting wallet connection...');
      console.log('📋 Available connectors:', connectors.map(c => ({ name: c.name, id: c.id })));

      // SIMPLE APPROACH: Just use the first available connector
      if (connectors.length === 0) {
        console.error('❌ No connectors available!');
        alert('No wallet connectors found. Please check your wagmi configuration.');
        return;
      }

      const connector = connectors[0];
      console.log('🎯 Using connector:', connector.name, connector.id);

      console.log('📞 Calling connect...');
      const result = connect({ connector });
      console.log('📞 Connect result:', result);

    } catch (error: any) {
      console.error('💥 Wallet connection failed:', error);
      console.error('💥 Error details:', {
        message: error.message,
        code: error.code,
        stack: error.stack
      });

      // Show user-friendly error
      alert(`Connection failed: ${error.message || 'Unknown error'}`);
    }
  };

  // Handle wallet disconnection
  const handleDisconnect = () => {
    disconnect();
  };

  return (
    <div>
      <button
        className="connect-wallet-btn rounded pixelated-text"
        onClick={isAuthenticated ? handleDisconnect : handleConnect}
        disabled={isPending || authLoading}
        style={{ marginBottom: '10px' }}
      >
        {isPending
          ? 'Connecting...'
          : authLoading
          ? 'Authenticating...'
          : isAuthenticated
          ? 'Disconnect'
          : isConnected
          ? 'Authenticate'
          : 'Connect Wallet'}
      </button>

      {/* DEBUG INFO */}
      <div style={{ fontSize: '10px', color: '#ccc', textAlign: 'left' }}>
        Debug: Connected={isConnected ? 'Yes' : 'No'} |
        Auth={isAuthenticated ? 'Yes' : 'No'} |
        Connectors={connectors.length} |
        Pending={isPending ? 'Yes' : 'No'}
      </div>
    </div>
  );
}
