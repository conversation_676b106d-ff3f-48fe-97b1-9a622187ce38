import { useConnect, useAccount, useDisconnect } from 'wagmi';
import { useEffect } from 'preact/hooks';
import { useAuth } from '../../context/AuthContext';
import { isMobile } from '../../utils/wallet-utils';

export function ConnectWallet() {
  const { connect, connectors, isPending } = useConnect();
  const { address, isConnected } = useAccount();
  const { disconnect } = useDisconnect();
  const { login, isAuthenticated, isLoading: authLoading } = useAuth();

  // Auto-login when wallet connects
  useEffect(() => {
    if (isConnected && address && !isAuthenticated && !authLoading) {
      console.log('Wallet connected, attempting auto-login...');
      login();
    }
  }, [isConnected, address, isAuthenticated, authLoading, login]);

  // Simple and universal wallet connection
  const handleConnect = async () => {
    try {
      // If already connected, just try to authenticate
      if (isConnected) {
        console.log('Already connected, attempting authentication...');
        await login();
        return;
      }

      console.log('Connecting wallet...');

      // For mobile devices, try direct provider connection first
      if (isMobile() && window.ethereum) {
        try {
          console.log('Mobile device detected, trying direct provider connection...');
          await window.ethereum.request({ method: 'eth_requestAccounts' });

          // Wait a moment for the connection to establish
          await new Promise(resolve => setTimeout(resolve, 500));

          // If this worked, the useEffect will trigger auto-login
          return;
        } catch (directError: any) {
          console.log('Direct provider connection failed, falling back to wagmi connectors:', directError);
          // Continue to wagmi connector logic below
        }
      }

      // Use wagmi connectors - let user choose or use the best available
      let connector;

      // On mobile, prefer injected connector
      if (isMobile()) {
        connector = connectors.find(c => c.id === 'injected') || connectors[0];
      } else {
        // On desktop, prefer MetaMask if available
        connector = connectors.find(c => c.name === 'MetaMask') || connectors[0];
      }

      if (!connector) {
        console.error('No wallet connectors available');
        return;
      }

      console.log('Using connector:', connector.name);
      connect({ connector });

    } catch (error: any) {
      console.error('Wallet connection failed:', error);

      // Don't show error for user rejection
      if (error.code === 4001) {
        console.log('User rejected connection');
        return;
      }

      // For other errors, you might want to show a user-friendly message
      console.error('Connection error:', error.message);
    }
  };

  // Handle wallet disconnection
  const handleDisconnect = () => {
    disconnect();
  };

  return (
    <button 
      className="connect-wallet-btn rounded pixelated-text"
      onClick={isAuthenticated ? handleDisconnect : handleConnect}
      disabled={isPending || authLoading}
    >
      {isPending
        ? 'Connecting...'
        : authLoading
        ? 'Authenticating...'
        : isAuthenticated
        ? 'Disconnect'
        : isConnected
        ? 'Authenticate'
        : 'Connect Wallet'}
    </button>
  );
}
