import { createContext, useContext, useState, useEffect } from 'preact/compat';
import type { ComponentChildren } from 'preact';
import { useAccount, useDisconnect, useSignMessage } from 'wagmi';
import { authApi } from '../services/api';
import { isMobile, handleWalletError } from '../utils/wallet-utils';

interface AuthContextType {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: any | null;
  login: () => Promise<void>;
  logout: () => void;
  error: string | null;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ComponentChildren }) {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [user, setUser] = useState<any | null>(null);
  const [error, setError] = useState<string | null>(null);
  
  const { address, isConnected } = useAccount();
  const { disconnect } = useDisconnect();
  const { signMessageAsync } = useSignMessage();

  // Check if user is already authenticated on mount
  useEffect(() => {
    const checkAuth = async () => {
      const token = localStorage.getItem('accessToken');
      if (token) {
        try {
          const profile = await authApi.getProfile();
          setUser(profile);
          setIsAuthenticated(true);
        } catch (error) {
          // Token is invalid or expired
          localStorage.removeItem('accessToken');
          setIsAuthenticated(false);
          setUser(null);
        }
      }
      setIsLoading(false);
    };

    checkAuth();
  }, []);

  // Login function to authenticate user with wallet
  const login = async () => {
    // For manual login, try to get address from window.ethereum if wagmi doesn't have it yet
    let walletAddress = address;

    if (!walletAddress && window.ethereum) {
      try {
        const accounts = await window.ethereum.request({ method: 'eth_accounts' });
        walletAddress = accounts[0];
        console.log('Got address from direct provider:', walletAddress);
      } catch (error) {
        console.error('Failed to get address from provider:', error);
      }
    }

    if (!walletAddress) {
      console.error('No wallet address available');
      setError('Wallet not connected');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      console.log('Starting authentication process with address:', address);
      
      // Create a message for the user to sign
      const timestamp = Date.now();
      const message = `Sign this message to authenticate with The Cave: ${timestamp}`;
      console.log('Message to sign:', message);
      
      // Request signature from the wallet
      console.log('Requesting signature...');
      let signature;
      
      try {
        // Add small delay for mobile devices to ensure wallet is ready
        if (isMobile()) {
          console.log('Mobile device detected, adding delay for wallet readiness...');
          await new Promise(resolve => setTimeout(resolve, 500));
        }

        signature = await signMessageAsync({
          message,
          account: walletAddress as `0x${string}`
        });
        console.log('Signature received:', signature);
      } catch (signError: any) {
        console.error('Signature error:', signError);

        const errorMessage = handleWalletError(signError);
        setError(errorMessage);

        setIsLoading(false);
        return;
      }
      
      // Send the signature to the backend for verification
      console.log('Sending signature to backend...');
      try {
        const authResponse = await authApi.signin(message, signature, walletAddress);
        console.log('Auth response:', authResponse);
        
        // Save the access token
        localStorage.setItem('accessToken', authResponse.accessToken);
        
        // Get user profile
        console.log('Getting user profile...');
        const profile = await authApi.getProfile();
        console.log('User profile:', profile);
        setUser(profile);
        setIsAuthenticated(true);
        
        // Redirect to dashboard
        window.location.href = '/dashboard';
      } catch (apiError: any) {
        console.error('API error:', apiError);
        if (apiError.response) {
          console.error('API response:', apiError.response.data);
          setError(apiError.response.data.message || 'Authentication failed');
        } else {
          setError(apiError.message || 'Authentication failed');
        }
      }
    } catch (error: any) {
      console.error('Authentication error:', error);
      setError(error.message || 'Authentication failed');
      setIsAuthenticated(false);
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  // Logout function
  const logout = () => {
    localStorage.removeItem('accessToken');
    setIsAuthenticated(false);
    setUser(null);
    disconnect();
  };

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        isLoading,
        user,
        login,
        logout,
        error,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

// Custom hook to use the auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
