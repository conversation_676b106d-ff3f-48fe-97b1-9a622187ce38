import { useState } from 'preact/hooks';

export function SimpleWalletTest() {
  const [status, setStatus] = useState('Ready');
  const [address, setAddress] = useState('');
  const [error, setError] = useState('');

  const testDirectConnection = async () => {
    try {
      setStatus('Testing...');
      setError('');
      
      console.log('🔍 Checking for wallet...');
      
      if (!window.ethereum) {
        throw new Error('No wallet found');
      }
      
      console.log('✅ Wallet found:', window.ethereum);
      
      console.log('📞 Requesting accounts...');
      const accounts = await window.ethereum.request({
        method: 'eth_requestAccounts'
      });
      
      console.log('✅ Accounts:', accounts);
      
      if (accounts && accounts.length > 0) {
        setAddress(accounts[0]);
        setStatus('Connected!');
      } else {
        throw new Error('No accounts returned');
      }
      
    } catch (err: any) {
      console.error('❌ Error:', err);
      setError(err.message);
      setStatus('Failed');
    }
  };

  const testGetAccounts = async () => {
    try {
      setStatus('Getting accounts...');
      setError('');
      
      if (!window.ethereum) {
        throw new Error('No wallet found');
      }
      
      const accounts = await window.ethereum.request({
        method: 'eth_accounts'
      });
      
      console.log('Current accounts:', accounts);
      
      if (accounts && accounts.length > 0) {
        setAddress(accounts[0]);
        setStatus('Already connected!');
      } else {
        setStatus('Not connected');
      }
      
    } catch (err: any) {
      console.error('Error getting accounts:', err);
      setError(err.message);
      setStatus('Failed');
    }
  };

  return (
    <div style={{ 
      padding: '20px', 
      border: '2px solid #ccc', 
      margin: '20px',
      backgroundColor: '#f9f9f9',
      fontFamily: 'monospace'
    }}>
      <h3>🧪 Simple Wallet Test</h3>
      
      <div style={{ marginBottom: '10px' }}>
        <strong>Status:</strong> {status}
      </div>
      
      {address && (
        <div style={{ marginBottom: '10px' }}>
          <strong>Address:</strong> {address.slice(0, 6)}...{address.slice(-4)}
        </div>
      )}
      
      {error && (
        <div style={{ marginBottom: '10px', color: 'red' }}>
          <strong>Error:</strong> {error}
        </div>
      )}
      
      <div style={{ marginBottom: '10px' }}>
        <strong>Has Ethereum:</strong> {window.ethereum ? 'Yes' : 'No'}
      </div>
      
      <div style={{ marginBottom: '20px' }}>
        <strong>User Agent:</strong> {navigator.userAgent.substring(0, 50)}...
      </div>
      
      <div>
        <button 
          onClick={testGetAccounts}
          style={{ 
            marginRight: '10px', 
            padding: '10px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            cursor: 'pointer'
          }}
        >
          Check Current Connection
        </button>
        
        <button 
          onClick={testDirectConnection}
          style={{ 
            padding: '10px',
            backgroundColor: '#28a745',
            color: 'white',
            border: 'none',
            cursor: 'pointer'
          }}
        >
          Connect Wallet
        </button>
      </div>
      
      <div style={{ marginTop: '20px', fontSize: '12px', color: '#666' }}>
        <strong>Debug Info:</strong>
        <pre>{JSON.stringify({
          hasEthereum: !!window.ethereum,
          isMetaMask: window.ethereum?.isMetaMask,
          isCoinbase: window.ethereum?.isCoinbaseWallet,
          userAgent: navigator.userAgent.substring(0, 100)
        }, null, 2)}</pre>
      </div>
    </div>
  );
}
