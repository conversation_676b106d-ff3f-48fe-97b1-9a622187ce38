/**
 * Utility functions for wallet detection and mobile compatibility
 */

/**
 * Detect if the current browser is MetaMask mobile web3 browser
 */
export const isMobileMetaMask = (): boolean => {
  // First check if we're in a browser environment
  if (typeof window === 'undefined' || typeof navigator === 'undefined') {
    return false;
  }
  
  const userAgent = navigator.userAgent || '';
  // Simple and reliable detection for MetaMask mobile browser
  const isMetaMask = /MetaMask/i.test(userAgent);
  const isMobile = /Mobile/i.test(userAgent);
  const hasEthereum = !!window.ethereum;
  
  // Check if it's a mobile MetaMask browser with web3 provider
  return isMetaMask && isMobile && hasEthereum;
};

/**
 * Detect if the current browser is any mobile browser
 */
export const isMobileBrowser = (): boolean => {
  const userAgent = navigator.userAgent || '';
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
};

/**
 * Get the appropriate connector for the current environment
 */
export const getRecommendedConnector = (connectors: any[]): any => {
  if (isMobileMetaMask()) {
    // For mobile MetaMask, use injected connector
    return connectors.find(c => c.id === 'injected') || 
           connectors.find(c => c.name === 'MetaMask') || 
           connectors[0];
  }
  
  // For desktop, prefer MetaMask connector
  return connectors.find(c => c.name === 'MetaMask') || connectors[0];
};

/**
 * Add appropriate delay for mobile browsers
 */
export const getMobileDelay = (): number => {
  return isMobileMetaMask() ? 1000 : 0;
};

/**
 * Handle common mobile wallet errors
 */
export const handleMobileWalletError = (error: any): string => {
  if (!error) return 'Unknown error occurred';
  
  const errorMessage = error.message || error.toString();
  
  // MetaMask mobile specific errors
  if (error.code === 4001 || errorMessage.includes('User rejected')) {
    return 'Signature request was rejected. Please try again.';
  }
  
  if (error.code === -32603 || errorMessage.includes('Internal error')) {
    return 'MetaMask mobile browser issue. Please refresh the page and try again.';
  }
  
  if (errorMessage.includes('timeout') || errorMessage.includes('Timeout')) {
    return 'Request timed out. Please check your connection and try again.';
  }
  
  if (errorMessage.includes('network') || errorMessage.includes('Network')) {
    return 'Network error. Please check your internet connection.';
  }
  
  return errorMessage;
};

/**
 * Check if the wallet is properly connected and ready for signature
 */
export const isWalletReady = async (address?: string): Promise<boolean> => {
  if (!address) return false;
  
  // Check if we're in a mobile environment
  if (isMobileMetaMask()) {
    // Add small delay for mobile to ensure wallet is fully ready
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  return true;
};