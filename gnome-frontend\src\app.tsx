import './app.css';
import { useState, useEffect } from 'preact/hooks';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { config } from './config/wagmi';
import { WagmiProvider } from 'wagmi';
import { AuthProvider, useAuth } from './context/AuthContext';
import { ProtectedRoute } from './components/auth/ProtectedRoute';
import { Dashboard } from './pages/dashboard/Dashboard';
import { Store } from './pages/store/Store';
import { Navbar } from './components/navigation/Navbar';
import { ConnectWallet } from './components/wallet/ConnectWallet';
import { ChainGuard } from './components/wallet/ChainGuard';
import { SimpleWalletTest } from './components/wallet/SimpleWalletTest';
const splashBackground = 'https://thecave.ams3.cdn.digitaloceanspaces.com/backgroundsplashfixed.png';

// Create a client for React Query
const queryClient = new QueryClient();

// Home component (splash screen)
function Home() {
  const { isAuthenticated, isLoading } = useAuth();
  const [activeGnomes] = useState(1727);

  // Redirect to dashboard if authenticated
  useEffect(() => {
    if (isAuthenticated && !isLoading) {
      window.location.href = '/dashboard';
    }
  }, [isAuthenticated, isLoading]);

  return (
    <div 
      className="min-h-screen w-full flex flex-col relative"
      style={{
        backgroundImage: `url(${splashBackground})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }}
    >
      {/* Header */}
      <header className="w-full p-6 flex justify-between items-center z-10">
        <div className="text-white text-xl md:text-2xl pixelated-text">The Cave</div>
        <ConnectWallet />
      </header>
      
      {/* Main content */}
      <main className="flex-grow flex flex-col items-center justify-center text-center px-4 z-10">
        <h1 className="text-4xl md:text-6xl text-white mb-6 pixelated-text">Welcome to the Cave</h1>
        <p className="text-white/80 mb-10 max-w-lg pixelated-text text-sm">
          Connect your wallet to start mining resources and earn rewards in the cave.
        </p>
        
        <ConnectWallet />

        {/* Temporary test component */}
        <SimpleWalletTest />

        <div className="text-white text-lg md:text-xl pixelated-text mt-16">
          {activeGnomes} Current Active Gnomes Mining
        </div>
      </main>
      
      {/* Overlay to darken the background image if needed */}
      <div className="absolute inset-0 bg-black/40 z-0"></div>
    </div>
  );
}

// Main App component with routing
export function App() {
  const [currentPath, setCurrentPath] = useState(window.location.pathname);

  // Handle route changes
  useEffect(() => {
    const handleRouteChange = () => {
      setCurrentPath(window.location.pathname);
    };

    window.addEventListener('popstate', handleRouteChange);
    
    return () => {
      window.removeEventListener('popstate', handleRouteChange);
    };
  }, []);

  return (
    <WagmiProvider config={config}>
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <ChainGuard>
            {currentPath === '/' && <Home />}
            {currentPath !== '/' && <Navbar />}
            {currentPath === '/dashboard' && (
              <ProtectedRoute>
                <Dashboard />
              </ProtectedRoute>
            )}
            {currentPath === '/store' && (
              <ProtectedRoute>
                <Store />
              </ProtectedRoute>
            )}
          </ChainGuard>
        </AuthProvider>
      </QueryClientProvider>
    </WagmiProvider>
  );
}
