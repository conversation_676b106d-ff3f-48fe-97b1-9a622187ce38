import { useWeb3Auth } from '../../context/Web3AuthContext';

export function Web3ConnectWallet() {
  const { 
    isAuthenticated, 
    isLoading, 
    isConnected, 
    address, 
    isConnecting, 
    error, 
    walletError,
    connect, 
    disconnect, 
    login 
  } = useWeb3Auth();

  const handleConnect = async () => {
    if (isConnected && !isAuthenticated) {
      // Already connected, just need to authenticate
      await login();
    } else {
      // Need to connect wallet first
      const success = await connect();
      if (success) {
        // Auto-login will be triggered by useEffect in context
        console.log('Wallet connected, waiting for auto-login...');
      }
    }
  };

  const handleDisconnect = () => {
    disconnect();
  };

  // Show error if any
  const displayError = error || walletError;

  return (
    <div>
      <button 
        className="connect-wallet-btn rounded pixelated-text"
        onClick={isAuthenticated ? handleDisconnect : handleConnect}
        disabled={isConnecting || isLoading}
      >
        {isConnecting
          ? 'Connecting...'
          : isLoading
          ? 'Authenticating...'
          : isAuthenticated
          ? 'Disconnect'
          : isConnected
          ? 'Authenticate'
          : 'Connect Wallet'}
      </button>
      
      {/* Show error message */}
      {displayError && (
        <div style={{ 
          color: 'red', 
          fontSize: '12px', 
          marginTop: '5px',
          maxWidth: '200px'
        }}>
          {displayError}
        </div>
      )}
      
      {/* Show address when connected */}
      {isConnected && address && (
        <div style={{ 
          color: '#ccc', 
          fontSize: '10px', 
          marginTop: '5px'
        }}>
          {address.slice(0, 6)}...{address.slice(-4)}
        </div>
      )}
    </div>
  );
}
